package rest

import (
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
)

type personResult struct {
	ID             string          `json:"id"`
	Name           string          `json:"name"`
	Father<PERSON>astName string          `json:"father_last_name"`
	MotherLastName string          `json:"mother_last_name"`
	Email          string          `json:"email"`
	Address        string          `json:"address"`
	Phone          string          `json:"phone"`
	BirthDate      *utils.DateOnly `json:"birth_date"`
	Gender         bool            `json:"gender"`
	Document       string          `json:"document"`
	DocumentType   int8            `json:"document_type"`
	CreatedAt      *time.Time      `json:"created_at"`
	UpdatedAt      *time.Time      `json:"updated_at"`
	DeletedAt      *time.Time      `json:"deleted_at"`
}

type clientResult struct {
	ID        string       `json:"id"`
	Person    personResult `json:"person"`
	CreatedAt *time.Time   `json:"created_at"`
	UpdatedAt *time.Time   `json:"updated_at"`
	DeletedAt *time.Time   `json:"deleted_at"`
}

func clientToResult(client *model.Client) clientResult {
	return clientResult{
		ID: client.ID,
		Person: personResult{
			ID:             client.Person.ID,
			Name:           client.Person.Name,
			FatherLastName: client.Person.FatherLastName,
			MotherLastName: client.Person.MotherLastName,
			Email:          client.Person.Email,
			Address:        client.Person.Address,
			Phone:          client.Person.Phone,
			BirthDate:      client.Person.BirthDate,
			Gender:         client.Person.Gender,
			Document:       client.Person.Document,
			DocumentType:   client.Person.DocumentType,
			CreatedAt:      client.Person.CreatedAt,
			UpdatedAt:      client.Person.UpdatedAt,
			DeletedAt:      client.Person.DeletedAt,
		},
		CreatedAt: client.CreatedAt,
		UpdatedAt: client.UpdatedAt,
		DeletedAt: client.DeletedAt,
	}
}
