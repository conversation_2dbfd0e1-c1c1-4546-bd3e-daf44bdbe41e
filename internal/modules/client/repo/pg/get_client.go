package pg

import (
	"context"
	"fmt"
	"time"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (c *clientPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Client, error) {
	var client model.Client
	var personID string

	err := pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := fmt.Sprintf(`
            SELECT id, person_id, created_at, updated_at, deleted_at
            FROM clients
            WHERE %s = $1 AND deleted_at IS NULL
        `, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&client.ID,
			&personID,
			&client.CreatedAt,
			&client.UpdatedAt,
			&client.DeletedAt,
		)

		if err != nil {
			return model.ClientNotFoundf(
				fmt.Sprintf("client with %s: %s not found", prop, value),
				err,
				nil,
			)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Fetch the person
	personQuery := `
		SELECT id, name, father_last_name, mother_last_name, email, address, phone,
			birth_date, gender, document, document_type, created_at, updated_at, deleted_at
		FROM persons
		WHERE id = $1 AND deleted_at IS NULL
	`

	var person personModel.Person
	var birthDate *time.Time
	err = pg.ExecuteInSchema(ctx, c.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		row := conn.QueryRow(ctx, personQuery, personID)
		return row.Scan(
			&person.ID,
			&person.Name,
			&person.FatherLastName,
			&person.MotherLastName,
			&person.Email,
			&person.Address,
			&person.Phone,
			&birthDate,
			&person.Gender,
			&person.Document,
			&person.DocumentType,
			&person.CreatedAt,
			&person.UpdatedAt,
			&person.DeletedAt,
		)
	})

	if err != nil {
		return nil, err
	}

	if birthDate != nil {
		person.BirthDate = &utils.DateOnly{Time: *birthDate}
	}

	client.Person = &person
	return &client, nil
}
