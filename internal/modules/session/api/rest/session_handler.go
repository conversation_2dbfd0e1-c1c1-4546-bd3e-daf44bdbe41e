package rest

import (
	"net/http"

	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type SessionHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	GetByClientAndTurn(w http.ResponseWriter, r *http.Request)
	GetByWorkerAndTurn(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
}

type sessionHandler struct {
	useCase   model.SessionUsecase
	validator *validator.Validate
	log       *logrus.Logger
}

func NewSessionHandler(
	useCase model.SessionUsecase,
	validator *validator.Validate,
	log *logrus.Logger,
) SessionHandler {
	return &sessionHandler{
		useCase:   useCase,
		validator: validator,
		log:       log,
	}
}
