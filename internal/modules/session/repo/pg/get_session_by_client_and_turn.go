package pg

import (
	"context"
	"time"

	clientModel "github.com/JosueDiazC/schedhold-backend/internal/modules/client/model"
	personModel "github.com/JosueDiazC/schedhold-backend/internal/modules/person/model"
	"github.com/JosueDiazC/schedhold-backend/internal/modules/session/model"
	workerModel "github.com/JosueDiazC/schedhold-backend/internal/modules/worker/model"
	"github.com/JosueDiazC/schedhold-backend/internal/utils"
	"github.com/JosueDiazC/schedhold-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (s *sessionPostgreRepo) GetByClientAndTurn(ctx context.Context, clientID string, turnID string) ([]model.Session, error) {
	var sessions []model.Session

	err := pg.ExecuteInSchema(ctx, s.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
            SELECT s.id, s.client_id, s.worker_id, s.turn_id, s.day, s.time, s.created_at, s.updated_at, s.deleted_at,
                   c.id, c.created_at, c.updated_at, c.deleted_at,
                   cp.id, cp.name, cp.father_last_name, cp.mother_last_name, cp.email, cp.address, cp.phone,
                   cp.birth_date, cp.gender, cp.document, cp.document_type, cp.created_at, cp.updated_at, cp.deleted_at,
                   w.id, w.positions, w.created_at, w.updated_at, w.deleted_at,
                   wp.id, wp.name, wp.father_last_name, wp.mother_last_name, wp.email, wp.address, wp.phone,
                   wp.birth_date, wp.gender, wp.document, wp.document_type, wp.created_at, wp.updated_at, wp.deleted_at
            FROM sessions s
            LEFT JOIN clients c ON s.client_id = c.id AND c.deleted_at IS NULL
            LEFT JOIN persons cp ON c.person_id = cp.id AND cp.deleted_at IS NULL
            JOIN workers w ON s.worker_id = w.id AND w.deleted_at IS NULL
            JOIN persons wp ON w.person_id = wp.id AND wp.deleted_at IS NULL
            WHERE s.client_id = $1 AND s.turn_id = $2 AND s.deleted_at IS NULL
        `

		rows, err := conn.Query(ctx, query, clientID, turnID)
		if err != nil {
			return utils.InternalErrorf("failed to query sessions", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var session model.Session
			var dbClientID, workerID *string
			var client clientModel.Client
			var clientPerson personModel.Person
			var worker workerModel.Worker
			var workerPerson personModel.Person
			var clientBirthDate, workerBirthDate *time.Time

			err := rows.Scan(
				&session.ID,
				&dbClientID,
				&workerID,
				&session.TurnID,
				&session.Day,
				&session.Time,
				&session.CreatedAt,
				&session.UpdatedAt,
				&session.DeletedAt,
				&client.ID,
				&client.CreatedAt,
				&client.UpdatedAt,
				&client.DeletedAt,
				&clientPerson.ID,
				&clientPerson.Name,
				&clientPerson.FatherLastName,
				&clientPerson.MotherLastName,
				&clientPerson.Email,
				&clientPerson.Address,
				&clientPerson.Phone,
				&clientBirthDate,
				&clientPerson.Gender,
				&clientPerson.Document,
				&clientPerson.DocumentType,
				&clientPerson.CreatedAt,
				&clientPerson.UpdatedAt,
				&clientPerson.DeletedAt,
				&worker.ID,
				&worker.Positions,
				&worker.CreatedAt,
				&worker.UpdatedAt,
				&worker.DeletedAt,
				&workerPerson.ID,
				&workerPerson.Name,
				&workerPerson.FatherLastName,
				&workerPerson.MotherLastName,
				&workerPerson.Email,
				&workerPerson.Address,
				&workerPerson.Phone,
				&workerBirthDate,
				&workerPerson.Gender,
				&workerPerson.Document,
				&workerPerson.DocumentType,
				&workerPerson.CreatedAt,
				&workerPerson.UpdatedAt,
				&workerPerson.DeletedAt,
			)

			if err != nil {
				return utils.InternalErrorf("failed to scan session", err, nil)
			}

			// Set client only if clientID is not null
			if dbClientID != nil {
				if clientBirthDate != nil {
					clientPerson.BirthDate = &utils.DateOnly{Time: *clientBirthDate}
				}
				client.Person = &clientPerson
				session.Client = &client
			}

			// Set worker
			if workerBirthDate != nil {
				workerPerson.BirthDate = &utils.DateOnly{Time: *workerBirthDate}
			}
			worker.Person = &workerPerson
			session.Worker = &worker

			sessions = append(sessions, session)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("error iterating sessions", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return sessions, nil
}
