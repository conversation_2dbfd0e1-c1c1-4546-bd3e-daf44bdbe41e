package model

import "context"

type SessionRepository interface {
	Create(ctx context.Context, session Session) error
	Update(ctx context.Context, session Session) error
	GetByProp(ctx context.Context, prop string, value string) (*Session, error)
	GetByClientAndTurn(ctx context.Context, clientID string, turnID string) ([]Session, error)
	GetByWorkerAndTurn(ctx context.Context, workerID string, turnID string) ([]Session, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]Session, error)
	Delete(ctx context.Context, id string) error
}
